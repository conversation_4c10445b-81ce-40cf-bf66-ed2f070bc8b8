# Gemini Code Assistant

一个集成了 Google Gemini AI 的 VS Code 扩展，为开发者提供智能代码助手功能。

## 功能特性

### 🤖 智能聊天界面
- 在 VS Code 侧边栏中提供专用的 AI 聊天界面
- 支持与 Gemini AI 进行自然语言对话
- 实时消息交互，支持代码块显示

### 💡 代码智能分析（开发中）
- 代码解释和说明
- 代码优化建议
- 自动生成注释
- 错误检测和修复建议

### 🎨 用户体验
- 完美适配 VS Code 主题
- 响应式聊天界面设计
- 支持键盘快捷键操作

## 安装和使用

### 开发环境要求
- VS Code 1.101.0 或更高版本
- Node.js 20.x 或更高版本
- TypeScript 5.8.3 或更高版本

### 开发和测试
1. 克隆项目到本地
2. 运行 `npm install` 安装依赖
3. 运行 `npm run compile` 编译项目
4. 按 `F5` 启动调试模式
5. 在新窗口中点击侧边栏的 Gemini 图标

### 使用方法
1. 点击 VS Code 左侧活动栏的 Gemini 图标
2. 在打开的聊天界面中输入你的问题
3. 按 Enter 或点击发送按钮与 AI 对话

## 配置设置

目前扩展不需要额外配置，未来版本将支持：
- Gemini API 密钥设置
- 聊天历史保存选项
- 界面主题自定义

## 已知问题

- 目前使用模拟的 AI 回复，需要集成真实的 Gemini API
- 代码解释功能仍在开发中
- 聊天历史不会持久保存

## 更新日志

### 0.0.1 (当前版本)

**初始版本功能：**
- ✅ 基础聊天界面实现
- ✅ VS Code 侧边栏集成
- ✅ 消息发送和接收机制
- ✅ 响应式界面设计
- ✅ VS Code 主题适配

**计划中的功能：**
- 🔄 Gemini API 集成
- 🔄 代码分析功能
- 🔄 右键菜单集成
- 🔄 聊天历史保存

---

## Following extension guidelines

Ensure that you've read through the extensions guidelines and follow the best practices for creating your extension.

* [Extension Guidelines](https://code.visualstudio.com/api/references/extension-guidelines)

## Working with Markdown

You can author your README using Visual Studio Code. Here are some useful editor keyboard shortcuts:

* Split the editor (`Cmd+\` on macOS or `Ctrl+\` on Windows and Linux).
* Toggle preview (`Shift+Cmd+V` on macOS or `Shift+Ctrl+V` on Windows and Linux).
* Press `Ctrl+Space` (Windows, Linux, macOS) to see a list of Markdown snippets.

## For more information

* [Visual Studio Code's Markdown Support](http://code.visualstudio.com/docs/languages/markdown)
* [Markdown Syntax Reference](https://help.github.com/articles/markdown-basics/)

**Enjoy!**
