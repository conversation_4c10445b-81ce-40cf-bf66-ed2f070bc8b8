{"name": "gemini-code-assistant", "displayName": "Gemini code assistant", "description": "Your gemini assistant in VS Code", "version": "0.0.1", "engines": {"vscode": "^1.101.0"}, "categories": ["Other"], "activationEvents": [], "main": "./out/extension.js", "contributes": {"viewsContainers": {"activitybar": [{"id": "gemini-code-assistant-sidebar", "title": "Gemini Assistant", "icon": "resources/icon.svg"}]}, "views": {"gemini-code-assistant-sidebar": [{"id": "gemini-code-assistant-chat", "name": "Chat with AI"}]}, "commands": [{"command": "gemini-code-assistant.helloWorld", "title": "Hello World"}]}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src", "test": "vscode-test"}, "devDependencies": {"@types/vscode": "^1.101.0", "@types/mocha": "^10.0.10", "@types/node": "20.x", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "eslint": "^9.25.1", "typescript": "^5.8.3", "@vscode/test-cli": "^0.0.10", "@vscode/test-electron": "^2.5.2"}}