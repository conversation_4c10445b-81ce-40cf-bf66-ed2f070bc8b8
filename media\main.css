body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    color: var(--vscode-editor-foreground);
    background-color: var(--vscode-editor-background);
    margin: 0;
    padding: 0;
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow: hidden;
}

#chat-container {
    display: flex;
    flex-direction: column;
    height: 100%;
}

#messages {
    flex-grow: 1;
    overflow-y: auto;
    padding: 10px;
    scrollbar-width: thin; /* For Firefox */
    scrollbar-color: var(--vscode-scrollbarSlider-background) var(--vscode-editor-background); /* For Firefox */
}

#messages::-webkit-scrollbar {
    width: 8px;
}

#messages::-webkit-scrollbar-track {
    background: var(--vscode-editor-background);
}

#messages::-webkit-scrollbar-thumb {
    background: var(--vscode-scrollbarSlider-background);
    border-radius: 4px;
}

.message {
    margin-bottom: 10px;
    padding: 8px 12px;
    border-radius: 8px;
    line-height: 1.5;
    max-width: 90%;
}

.user-message {
    background-color: var(--vscode-settings-focusedRowBackground);
    color: var(--vscode-editor-foreground);
    align-self: flex-end;
    margin-left: auto;
}

.ai-message {
    background-color: var(--vscode-list-hoverBackground);
    color: var(--vscode-editor-foreground);
    align-self: flex-start;
    margin-right: auto;
}

#input-area {
    display: flex;
    padding: 10px;
    border-top: 1px solid var(--vscode-editorGroup-border);
    background-color: var(--vscode-editor-background);
}

#chat-input {
    flex-grow: 1;
    margin-right: 10px;
    padding: 8px;
    border: 1px solid var(--vscode-input-border);
    border-radius: 5px;
    background-color: var(--vscode-input-background);
    color: var(--vscode-input-foreground);
    outline: none;
    resize: none; /* 禁用文本区域的拖动调整大小 */
}

#chat-input:focus {
    border-color: var(--vscode-focusBorder);
}

#send-button {
    background-color: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
    border: none;
    border-radius: 5px;
    padding: 8px 15px;
    cursor: pointer;
    outline: none;
}

#send-button:hover {
    background-color: var(--vscode-button-hoverBackground);
}

/* 代码块样式 */
.code-block {
    background-color: var(--vscode-textCodeBlock-background);
    border: 1px solid var(--vscode-editorGroup-border);
    border-radius: 4px;
    padding: 8px;
    margin: 8px 0;
    font-family: var(--vscode-editor-font-family);
    font-size: var(--vscode-editor-font-size);
    overflow-x: auto;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid var(--vscode-progressBar-background);
    border-radius: 50%;
    border-top-color: var(--vscode-button-background);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}
