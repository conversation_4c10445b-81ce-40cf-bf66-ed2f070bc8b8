# Gemini Code Assistant - 项目结构

## 📁 项目文件结构

```
gemini-code-assistant/
├── 📄 package.json              # 扩展清单文件，定义了侧边栏视图容器
├── 📄 tsconfig.json            # TypeScript 配置
├── 📄 README.md                # 项目说明文档
├── 📄 TESTING.md               # 测试指南
├── 📄 PROJECT_STRUCTURE.md     # 项目结构说明（本文件）
├── 📁 src/                     # 源代码目录
│   ├── 📄 extension.ts         # 主扩展文件，包含 ChatWebviewProvider 类
│   └── 📁 test/                # 测试文件
├── 📁 out/                     # 编译输出目录
│   ├── 📄 extension.js         # 编译后的主文件
│   └── 📄 extension.js.map     # Source map 文件
├── 📁 resources/               # 资源文件目录
│   └── 📄 icon.svg             # 侧边栏图标
├── 📁 media/                   # Webview 前端资源
│   ├── 📄 main.css             # 聊天界面样式
│   └── 📄 main.js              # 聊天界面交互逻辑
└── 📁 node_modules/            # 依赖包目录
```

## 🔧 核心文件说明

### package.json
- 定义了扩展的基本信息和配置
- 配置了侧边栏视图容器 `gemini-code-assistant-sidebar`
- 注册了聊天视图 `gemini-code-assistant-chat`

### src/extension.ts
- **ChatWebviewProvider 类**: 管理 Webview 的生命周期
- **activate 函数**: 扩展激活时注册视图提供者
- **消息处理**: 处理前端发送的各种命令

### media/main.css
- VS Code 主题适配的样式
- 聊天界面布局和美化
- 响应式设计支持

### media/main.js
- 前端交互逻辑
- 消息发送和接收处理
- DOM 操作和事件监听

### resources/icon.svg
- 侧边栏显示的 Gemini 图标
- 使用渐变色彩设计

## 🚀 工作流程

1. **扩展激活**: VS Code 加载扩展时调用 `activate` 函数
2. **视图注册**: 注册 `ChatWebviewProvider` 到 VS Code
3. **界面创建**: 用户点击侧边栏图标时创建 Webview
4. **内容加载**: 加载 HTML、CSS、JS 到 Webview
5. **消息通信**: 前后端通过 `postMessage` 进行通信

## 📋 已实现功能

- ✅ 侧边栏集成
- ✅ 聊天界面 UI
- ✅ 消息发送接收
- ✅ VS Code 主题适配
- ✅ 基础错误处理

## 🔮 待开发功能

- 🔄 Gemini API 集成
- 🔄 代码分析功能
- 🔄 右键菜单集成
- 🔄 设置页面
- 🔄 聊天历史保存

## 🛠️ 开发命令

```bash
# 安装依赖
npm install

# 编译项目
npm run compile

# 监听文件变化并自动编译
npm run watch

# 运行测试
npm test

# 代码检查
npm run lint
```
