// 这个特殊的变量 vscode 是由 VS Code 注入到 Webview 环境中的
const vscode = acquireVsCodeApi();

const messagesDiv = document.getElementById('messages');
const chatInput = document.getElementById('chat-input');
const sendButton = document.getElementById('send-button');

// 滚动到底部
function scrollToBottom() {
    messagesDiv.scrollTop = messagesDiv.scrollHeight;
}

// 添加消息到聊天界面
function addMessage(text, sender) {
    const messageElement = document.createElement('div');
    messageElement.classList.add('message');
    messageElement.classList.add(sender === 'user' ? 'user-message' : 'ai-message');
    
    // 处理代码块和换行
    const formattedText = formatMessage(text);
    messageElement.innerHTML = formattedText;
    
    messagesDiv.appendChild(messageElement);
    scrollToBottom();
}

// 格式化消息，支持代码块和换行
function formatMessage(text) {
    // 处理代码块 ```code```
    text = text.replace(/```([\s\S]*?)```/g, '<div class="code-block">$1</div>');
    
    // 处理行内代码 `code`
    text = text.replace(/`([^`]+)`/g, '<code style="background-color: var(--vscode-textCodeBlock-background); padding: 2px 4px; border-radius: 3px;">$1</code>');
    
    // 处理换行
    text = text.replace(/\n/g, '<br>');
    
    return text;
}

// 显示加载状态
function showLoading() {
    const loadingElement = document.createElement('div');
    loadingElement.classList.add('message', 'ai-message');
    loadingElement.id = 'loading-message';
    loadingElement.innerHTML = '<div class="loading"></div> AI正在思考...';
    messagesDiv.appendChild(loadingElement);
    scrollToBottom();
}

// 隐藏加载状态
function hideLoading() {
    const loadingElement = document.getElementById('loading-message');
    if (loadingElement) {
        loadingElement.remove();
    }
}

// 处理发送消息
function sendMessage() {
    const text = chatInput.value.trim();
    if (text) {
        addMessage(text, 'user');
        showLoading();
        
        // 将消息发送到 VS Code 扩展的后端
        vscode.postMessage({
            command: 'sendMessage',
            text: text
        });
        
        chatInput.value = ''; // 清空输入框
    }
}

// 事件监听器
sendButton.addEventListener('click', sendMessage);

chatInput.addEventListener('keydown', (e) => {
    if (e.key === 'Enter' && !e.shiftKey) { // 按Enter发送，Shift+Enter换行
        e.preventDefault(); // 阻止默认的Enter行为（例如在textarea中换行）
        sendMessage();
    }
});

// 接收来自 VS Code 扩展的消息
window.addEventListener('message', event => {
    const message = event.data; // The JSON data our extension sent
    switch (message.command) {
        case 'receiveMessage':
            hideLoading();
            addMessage(message.text, 'ai');
            break;
        case 'showError':
            hideLoading();
            addMessage(`错误: ${message.text}`, 'ai');
            break;
        case 'clearChat':
            messagesDiv.innerHTML = '<div class="message ai-message">聊天记录已清空。你好！我是你的Gemini代码助手。</div>';
            break;
        // TODO: 其他类型消息的处理，例如代码替换命令的确认信息等
    }
});

// 初始滚动到底部，如果内容很多的话
scrollToBottom();
