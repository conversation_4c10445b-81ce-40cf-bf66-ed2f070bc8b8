import * as vscode from 'vscode';

// 定义 Webview 的自定义提供者
class ChatWebviewProvider implements vscode.WebviewViewProvider {
    // 存储对 Webview 视图的引用
    public static readonly viewType = 'gemini-code-assistant-chat'; // 确保这个ID与 package.json 中的 view.id 匹配

    private _view?: vscode.WebviewView;

    constructor(private readonly _extensionUri: vscode.Uri) {}

    // 当 VS Code 需要显示你的 Webview 视图时，会调用这个方法
    public resolveWebviewView(
        webviewView: vscode.WebviewView,
        context: vscode.WebviewViewResolveContext,
        _token: vscode.CancellationToken,
    ) {
        this._view = webviewView;

        webviewView.webview.options = {
            // 启用脚本
            enableScripts: true,
            // 限制 Webview 能够访问的本地资源
            localResourceRoots: [this._extensionUri],
        };

        // 设置 Webview 的 HTML 内容
        webviewView.webview.html = this._getHtmlForWebview(webviewView.webview);

        // 处理从 Webview 发送到扩展的消息
        webviewView.webview.onDidReceiveMessage(async message => {
            switch (message.command) {
                case 'sendMessage':
                    // TODO: 在这里处理来自Webview的消息，并调用AI API
                    // 例如：let aiResponse = await callGeminiApi(message.text);
                    // 然后将AI的回复发回Webview: this._view.webview.postMessage({ command: 'receiveMessage', text: aiResponse });

                    // 临时模拟AI回复
                    const response = `Gemini回复: 你说的是 "${message.text}"。我还需要连接到真正的Gemini模型才能给你智能回复。`;
                    this._view?.webview.postMessage({ command: 'receiveMessage', text: response });
                    return;
                case 'explainCode':
                    // 获取当前选中的代码
                    const editor = vscode.window.activeTextEditor;
                    if (editor) {
                        const selection = editor.selection;
                        const selectedText = editor.document.getText(selection);
                        // TODO: 将选中的代码发送给Gemini进行解释
                        const explainResponse = `Gemini解释了: 你选中了这段代码: \n\`\`\`\n${selectedText}\n\`\`\`\n （这里需要调用Gemini API来实际解释）`;
                        this._view?.webview.postMessage({ command: 'receiveMessage', text: explainResponse });
                    } else {
                        this._view?.webview.postMessage({ command: 'receiveMessage', text: '请先在编辑器中选中要解释的代码。' });
                    }
                    return;
                case 'generateComment':
                    // 类似 explainCode，但发送给Gemini的提示语是生成注释
                    return;
                case 'optimizeCode':
                    // 类似 explainCode，但发送给Gemini的提示语是优化代码，然后可能需要将Gemini返回的代码替换掉原来的
                    // 注意：替换代码逻辑会在后续实现
                    return;
                // ... 其他命令处理 (生成代码片段、查找错误等)
            }
        });
    }

    // 辅助方法：生成 Webview 的 HTML 内容
    private _getHtmlForWebview(webview: vscode.Webview) {
        // 获取 Webview 可访问的本地资源URI
        const styleUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'media', 'main.css'));
        const scriptUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'media', 'main.js'));

        // 你可以在这里添加更多的 CSP 规则来提高安全性
        const nonce = getNonce(); // 用于 Content Security Policy (CSP)

        return `<!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource}; script-src 'nonce-${nonce}';">
                <link href="${styleUri}" rel="stylesheet">
                <title>Gemini Chat</title>
            </head>
            <body>
                <div id="chat-container">
                    <div id="messages">
                        <div class="message ai-message">你好！我是你的Gemini代码助手。请在下面输入你的问题或在编辑器中选中代码后使用右键菜单与我互动。</div>
                    </div>
                    <div id="input-area">
                        <textarea id="chat-input" placeholder="输入你的消息..." rows="3"></textarea>
                        <button id="send-button">发送</button>
                    </div>
                </div>
                <script nonce="${nonce}" src="${scriptUri}"></script>
            </body>
            </html>`;
    }
}

// 帮助生成一个随机字符串用于 Content Security Policy (CSP)
function getNonce() {
    let text = '';
    const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    for (let i = 0; i < 32; i++) {
        text += possible.charAt(Math.floor(Math.random() * possible.length));
    }
    return text;
}

// 插件激活时执行的函数
export function activate(context: vscode.ExtensionContext) {
    console.log('Congratulations, your extension "gemini-code-assistant" is now active!');

    // 注册 Webview 视图提供者
    const chatProvider = new ChatWebviewProvider(context.extensionUri);
    context.subscriptions.push(
        vscode.window.registerWebviewViewProvider(ChatWebviewProvider.viewType, chatProvider)
    );

    // 示例命令，如果你想保留 yo code 自动生成的 helloWorld 命令，可以保留
    const disposable = vscode.commands.registerCommand('gemini-code-assistant.helloWorld', () => {
        vscode.window.showInformationMessage('Hello World from gemini-code-assistant!');
    });

    context.subscriptions.push(disposable);
}

// 插件停用时执行的函数
export function deactivate() {}
